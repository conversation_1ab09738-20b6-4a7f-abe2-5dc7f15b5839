import React, { useEffect, useRef, useState } from 'react'
import Taro from '@tarojs/taro'
import { View, Image } from '@tarojs/components'
import {
  Icon,
  Input,
  Animate
} from "@nutui/nutui-react-taro";
import { inject, observer } from 'mobx-react';
import BlueImg from '@/assets/images/ikon_shebei.png'
import IconShebei from '@/assets/images/icon_shebei.png'
import IconShuaxin from '@/assets/images/icon_shuaxin.png'

import './index.scss'

const BluetoothPage = observer(({ store }) => {
  const state = useRef({
    deviceId: '',
    characteristicId: '',
    serviceId: '',
    writeDefaultId: '',
    readId: '',
    localName: '未知'
  })

  const [dataSource, setDataSource] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [serach, setSearch] = useState('')

  useEffect(() => {
    Taro.hideHomeButton()
    // handleBegin()
    loadDate()
  }, [])

  const handleBegin = () => {
    if (loading) {
      return
    }
    setLoading(true)
    openBluetoothAdapter()
  }

  const loadDate = () => {
 
    //初始化蓝牙模块
    Taro.openBluetoothAdapter({
      success: (res) => {
        //获取本机蓝牙适配器状态。
        getBluetoothAdapterState()
      },
      fail: (res) => {
        if (res.errCode === 10001) {
          //手机蓝牙未打开，弹出提示框让用户打开

          //监听蓝牙适配器状态变化
          Taro.onBluetoothAdapterStateChange(function (res) {
            if (res.available) {

              //获取本机蓝牙适配器状态。
              getBluetoothAdapterState()
            }
          })
        }
      }
    })

  }

  const getBluetoothAdapterState = () => {
    Taro.getBluetoothAdapterState({
      success: function (res) {
        setTimeout(() => {
          getBluetoothDevices()
        }, 1000)
      },
      fail(res) {
        console.log(res)
      }
    })
  }

  // //获取搜索到的蓝牙设备列表
  const getBluetoothDevices = () => {
    setTimeout(() => {
      Taro.getBluetoothDevices({
        success: function (res) {
          console.log('getBluetoothDevices')
          console.log(res)
          if (res.devices.length > 0) {

          } else {
            //没有搜索到设备
            console.log("没有搜索到设备")
          }
        },
        fail(res) {
          console.log(res)
        }
      })
    }, 2000)
  }


  const getBLEDeviceServices = (deviceId) => {
    console.log('getBLEDeviceServices', deviceId)
    // let serviceId;
    Taro.getBLEDeviceServices({
      deviceId,
      success: (res) => {
        const uuid = res.services.find(item => !item.uuid.includes('00001800') && !item.uuid.includes('00001800'))
        console.log(uuid)
        getBLEDeviceCharacteristics(deviceId, res.services[0].uuid)
      }
    });
  }


  const stopBluetoothDevicesDiscovery = () => {
    Taro.stopBluetoothDevicesDiscovery({
      success(res) {
        console.log('停止搜索')
        console.log(res);
        setLoading(false)
      }
    });
    Taro.onBLEConnectionStateChange(function (res) {
      // 该方法回调中可以用于处理连接意外断开等异常情况
      if (!res.connected) {
        console.log('蓝牙已经断开----')
        Taro.showToast({
          title: '蓝牙已经断开!'
        });
        setLoading(false)
      }
    })

  }

  const getBLEDeviceCharacteristics = (deviceId, serviceId) => {

    Taro.getBLEDeviceCharacteristics({
      deviceId,
      serviceId,
      success: (res) => {
        console.log('getBLEDeviceCharacteristics')
        console.log(res)
        // const result = res.characteristics[0]
        //writeDefault  写
        //read 读
        const writeRelust = res.characteristics.find(value => value.properties.write)
        const readRelust = res.characteristics.find(value => value.properties.read)
        console.log(writeRelust)
        console.log(readRelust)
        if (writeRelust && readRelust) {
          state.current = {
            ...state.current,
            serviceId,
            writeDefaultId: writeRelust.uuid,
            readId: readRelust.uuid,
          }
          store.counterStore.setState(state.current)
          Taro.switchTab({
            url: '/pages/home/<USER>',
          })
        }
      },
      fail(err) {
        console.log(err, "获取失败");
      }
    });
  }


  const createBLEConnection = (items) => {
    const { deviceId } = items
    Taro.showLoading({
      title: '连接中...'
    });
    Taro.createBLEConnection({
      deviceId,
      success: (res) => {
        Taro.hideLoading();
        stopBluetoothDevicesDiscovery(); // 连接成功，停止搜索
        console.log(res, "连接成功");
        if (res.errCode == 0) {
          state.current = {
            ...state.current,
            deviceId,
            localName: items.localName || items.name
          }
          getBLEDeviceServices(deviceId); // 获取服务
        } else if (res.errCode == 10012) {
          Taro.showToast({
            title: '连接超时，请重试！'
          });
        }
      },
      fail(error) {
        Taro.hideLoading();
        console.log(error, '连接失败');
        Taro.showToast({
          title: '连接失败！'
        });
      }
    });

  }
  const watchBluetoothFound = () => {
    // const data = dataSource
    Taro.onBluetoothDeviceFound((res: any) => { // 监听搜索到的新设备
      try {
        console.log(res.devices, '获取新的设备')
        // const relust: any = []
        // const devices = res.devices
        // devices.map((items: any) => {
        //   const localName = items.localName || ''
        //   const name = items.name || ''
        //   if (name.includes('BAT') || name.includes('BLE-') || localName.includes('BAT') || localName.includes('BLE-')) {
        //     relust.push(items)
        //   }
        // })
        // console.log(watchBluetooth)
        // setWatchBluetooth(devices)
        // console.log()
        // console.log(data)
        // console.log([...dataSource, ...relust])
        //  setDataSource([...dataSource, ...relust])
      } catch (err) {

      }
    })
  }

  // const getBluetoothDevices = () => {

  //   Taro.getBluetoothDevices({ // 获取搜索到的设备
  //     success: (res: any) => {
  //       console.log(res, "搜索到的设备");
  //       console.log(res.devices)
  //       watchBluetoothFound();
  //       if (res.devices.length > 0) {
  //         let devicesListArr: any = res.devices.filter(item => {
  //           const name = item.localName || item.name
  //           return name.includes('BAT') || name.includes('BLE-')
  //         });
  //         Taro.hideLoading();

  //         setDataSource(res.devices)
  //       } else {
  //         Taro.hideLoading();
  //         // Taro.showModal({
  //         //   title: '温馨提示',
  //         //   content: '无法搜索到蓝牙设备，请重试',
  //         //   showCancel: false
  //         // });
  //         // setLoading(false)
  //         // Taro.closeBluetoothAdapter({ //关闭蓝牙模块
  //         //   success: (res) => {
  //         //     console.log(res, "关闭蓝牙模块");
  //         //   }
  //         // });
  //       }
  //     }
  //   });
  // }

  const searchBlue = () => {
    Taro.startBluetoothDevicesDiscovery({
      // services: [],
      // allowDuplicatesKey: false,
      // interval: 0,
      success: (res) => {
        console.log(res, "开始搜索设备");
        Taro.showLoading({
          title: '正在搜索设备'
        });
        getBluetoothDevices();
      },
      fail: (res) => {
        console.log(res, '搜索失败');
        Taro.showToast({
          title: '搜索蓝牙设备失败!',
          icon: 'none'
        });
      }
    });
  }


  const openBluetoothAdapter = () => {
    Taro.openBluetoothAdapter({ //初始化蓝牙模块
      success: (res) => {
        console.log(res, '初始化蓝牙成功');
        Taro.getBluetoothAdapterState({
          success: function (res) {
            if (!res.available) {
              Taro.showModal({
                title: '温馨提示',
                content: '蓝牙适配器不可用，请重新启动',
                showCancel: false
              });
              return
            }
            if (res.discovering) {
              Taro.getBluetoothDevices({ // 获取搜索到的设备
                success: (res: any) => {
                  console.log(res, "搜索到的设备11111111");
                  console.log(res.devices)
                }
              })
              return
            }
            searchBlue(); //开始搜索
          }
        })
        // Taro.getBluetoothAdapterState((res) => { // 监听蓝牙适配器状态变化
        //   console.log(res)

        //   // if (res.discovering) {

        //   //   return
        //   // }
        //   console.log(res)
        //   // searchBlue(); //开始搜索
        // });
        //console.log(res)

      },
      fail(res) {
        setLoading(false)
        console.log(res, '初始化蓝牙失败');
        Taro.showToast({
          title: '请检查手机蓝牙是否打开',
          icon: 'none'
        });
      }
    });
  }

  const handleStop = () => {
    setLoading(false)
  }

  const CloseBLEConnection = () => {

    Taro.closeBLEConnection({
      deviceId: state.current.deviceId,
      success: () => {
        Taro.showToast({
          title: '已断开连接'
        });
        setLoading(false)
        setDataSource([])
      }
    });
    closeBluetoothAdapter();
  }

  const closeBluetoothAdapter = () => {
    Taro.closeBluetoothAdapter({
      success: (res) => {
        console.log(res);
      }
    });
  }

  let dataSourceFilter = dataSource

  if (serach) {
    dataSourceFilter = dataSource.filter(item => (item.localName || '').includes(serach) || (item.name || '').includes(serach))
  }

  const handleBlurInput = (values) => {
    setSearch(values)
  }


  return (
    <View className="bluetooth-page">
      <View className='bluetooth-page-search'>
        <View className='bluetooth-page-search-block'>
          <Image className='bluetooth-page-search-block-img' src={BlueImg} />
          <View className="bluetooth-page-search-block-border"></View>
          {loading && <View className="scanning"></View>}
        </View>
        <View className='bluetooth-page-search-text'>
          {
            loading
              ?
              <Animate type="breath" loop>正在搜索设备，请稍后…</Animate>
              :
              '搜索已暂停'
          }
        </View>
      </View>
      <View className='bluetooth-page-title'>
        <View>扫描可用设备 </View>
        <Image onClick={handleBegin} src={IconShuaxin} className='bluetooth-page-title-img' />
      </View>
      <View className='bluetooth-page-SearchBar'>
        <Icon name="search"></Icon>
        <Input onBlur={handleBlurInput} placeholder='请输入关键字搜索设备' />
      </View>
      <View className='bluetooth-page-body-list'>
        {
          dataSourceFilter.map(items => {
            return (
              <View
                className='bluetooth-page-body-list-li'
                key={items.deviceId}
              >
                <View className='bluetooth-page-body-list-li-row'>
                  <Image className='bluetooth-page-body-list-li-img' src={IconShebei} />
                  <View className='bluetooth-page-body-list-li-text'> {items.name || items.localName}</View>
                </View>
                <View
                  className='bluetooth-page-body-list-li-button'
                  onClick={() => createBLEConnection(items)}>
                  连接
                </View>
              </View>
            )
          })
        }

      </View>
    </View >
  )
})

export default inject(store => store)(BluetoothPage);
