{"name": "jindu-intelligent-iot", "version": "1.0.0", "private": true, "description": "jindu-intelligent-iot", "templateInfo": {"name": "react-NutUI", "typescript": true, "css": "sass"}, "scripts": {"start": "NODE_ENV=production taro build --type weapp --watch", "build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "license": "MIT", "dependencies": {"@babel/runtime": "^7.7.7", "@nutui/nutui-react-taro": "^1.4.7", "@tarojs/components": "3.6.8", "@tarojs/helper": "3.6.8", "@tarojs/plugin-framework-react": "3.6.8", "@tarojs/plugin-html": "3.6.8", "@tarojs/plugin-platform-alipay": "3.6.8", "@tarojs/plugin-platform-h5": "3.6.8", "@tarojs/plugin-platform-jd": "3.6.8", "@tarojs/plugin-platform-qq": "3.6.8", "@tarojs/plugin-platform-swan": "3.6.8", "@tarojs/plugin-platform-tt": "3.6.8", "@tarojs/plugin-platform-weapp": "3.6.8", "@tarojs/react": "3.6.8", "@tarojs/runtime": "3.6.8", "@tarojs/shared": "3.6.8", "@tarojs/taro": "3.6.8", "lodash": "^4.17.21", "mobx": "^6.9.0", "mobx-react": "^7.6.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-pull-to-refresh": "^2.0.1", "react-refresh": "^0.11.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.8", "@tarojs/taro-loader": "3.6.8", "@tarojs/webpack5-runner": "3.6.8", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-router-dom": "^5.1.7", "@types/react-syntax-highlighter": "^13.5.2", "@types/react-test-renderer": "^18.0.0", "@types/react-transition-group": "^4.4.4", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "babel-plugin-import": "^1.13.3", "babel-preset-taro": "3.6.8", "eslint": "^8.12.0", "eslint-config-taro": "3.6.8", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "postcss": "^8.4.18", "style-loader": "1.3.0", "stylelint": "^14.4.0", "ts-node": "^10.9.1", "typescript": "^4.1.0", "webpack": "^5.78.0"}}