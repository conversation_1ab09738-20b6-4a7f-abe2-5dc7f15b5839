.user-page {
  padding: 16px;
  height: 130vh;
  background-color: #F6F6F6;

  &-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  &-avatar {
    width: 100px;
    Image{
      width:201rpx;
      height:201rpx;
      border-radius: 50%;
      box-shadow: 1rpx 2rpx 16rpx 11rpx rgb(230, 230, 230);
      background-color: white;
    }
  }

  &-title {
    font-size: 18px;
    margin-top: 10px;
    margin-bottom: 30px;
    font-weight: 100;
    // color: white;
  }
}
:root {
  --nutui-collapse-item-padding: 13px 0px 13px 16px;
}
.home-page-content-cell{
  display: flex;
  align-items: center;
}
.myDevice-page {
  // padding: 16px;
  // padding-top: 6px;

  &-CellGroup {
    margin-bottom: 16px;
  }
  .onUnbindBtn{
    height:20px;
    width: 45px;
    background-color: red;
    border-radius: 3px;
    color:white;
    text-align: center;
  }
  .switchBtn{
    height:20px;
    width: 45px;
    background-color: #3861E3;
    border-radius: 3px;
    color:white;
    text-align: center;
    &:hover{
      background-color: #7c9bff;
    }
  }
  .addEqu{
    display: flex;
    align-items: center;
    width: 561rpx;
    gap: 10px;
  }
  .switchList{
    width: 269rpx;
    display: flex;
    flex-direction: row-reverse;
    gap: 5%;
    Image{
      width: 20px;
    }
  }
  .nut-image {
    display: block;
    position: relative;
    width: 20px !important;
    height: 20px;
  }
}
.logo{
  width: 120px;
  height: 120px;
}

.editInput{
  display: flex;
  flex-wrap: nowrap;
  .sureBtn{
    line-height: 90rpx;
    text-align: center;
    background: #4CBBB1;
    color: white;
    width: 28%;
    border-radius: 0px 4px 4px 0px;
    z-index: 1001;
    &:active{
      background: red;
    }
  }
}
.Overlay1{
  width: 100%;
  height: 100%;
  z-index: 100;
  background-color: transparent;
  position: absolute;
  top: 0%;
  left: 0%;
  // border: 1px solid red;
}
.input1{
  z-index: 101;
}

//当前页面组件改动
.nut-collapse-item{
  // width: 95%;
  // margin-left:32rpx;
  border-radius: 5px;
  overflow: hidden;
  box-shadow:  0px 1px 7px 0px rgb(237, 238, 241);
}
.nut-collapse{
  width: 100%;
}
.nut-collapse-item__header{
  background-color: white;
}
.nut-collapse-item__content{
  background-color: white;
}
.nut-cell-group__wrap{
  background-color: white;
}
.nut-collapse-item{
  -webkit-box-shadow: 0px 1px 7px 0px transparent; 
  box-shadow: 0px 1px 7px 0px transparent;
}
.nut-input{
  background-color: white;
}
.nut-collapse-item__header{
  line-height:18px;
}
.home-page-content-cell{
  Image{
    width: 50rpx;
    height: 30px;
  }
}
.nut-collapse-item__header{
  padding: 13px 7px 13px 23px;
}
.nut-circleprogress{
  transform: translate3d(0,0,0);
  backface-visibility: hidden;
  will-change: transform;
  
  path {
    transition: stroke-dashoffset 0.6s ease-out;
  }
}