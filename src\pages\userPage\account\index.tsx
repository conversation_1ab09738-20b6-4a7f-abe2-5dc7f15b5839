import React, { useState } from 'react'
import { View } from '@tarojs/components'
import {
  Cell,
  CellGroup
} from "@nutui/nutui-react-taro";
import './index.scss'

function Index() {
  const [state, setState] = useState({
    dataSource: [
      {
        label: '接收时间',
        value: ''
      },
      {
        label: '电池编号',
        value: ''
      }
    ],
    column: [
      {
        title: '基础数据',
        dataSource: [
          {
            label: '电池总电压',
            value: '0V'
          },
          {
            label: '电流',
            value: '0A'
          },
          {
            label: '剩余容量百分比',
            value: '0%'
          },
          {
            label: '累积循环放电次数',
            value: '0次'
          },
          {
            label: '预计可用时间',
            value: '0h'
          },
          {
            label: '系统软件版本',
            value: '0'
          }
        ]
      },
      {
        title: '状态',
        dataSource: [
          {
            label: '电池状态',
            value: ''
          },
          {
            label: '充电 MOS 状态',
            value: ''
          },
          {
            label: '放电 MOS 状态',
            value: ''
          },
          {
            label: '强启状态',
            value: ''
          },
          {
            label: '智能加热状态',
            value: ''
          },
          {
            label: '加热功能状态',
            value: ''
          }
        ]
      },
      {
        title: '温度',
        dataSource: [
          {
            label: '电芯温度',
            value: '0℃'
          },
          {
            label: '箱体内部温度',
            value: '0℃'
          },
          {
            label: '充放电MOS温度',
            value: '0℃'
          },
          {
            label: '加热片1温度',
            value: '0℃'
          },
          {
            label: '加热片2温度',
            value: '0℃'
          }
        ]
      },
      {
        title: '电芯单体电压',
        dataSource: [
          {
            label: '第1节电芯电压',
            value: '0'
          },
          {
            label: '第2节电芯电压',
            value: '0'
          },
          {
            label: '第3节电芯电压',
            value: '0'
          },
          {
            label: '第4节电芯电压',
            value: '0'
          },
          {
            label: '第5节电芯电压',
            value: '0'
          },
          {
            label: '第6节电芯电压',
            value: '0'
          },
          {
            label: '第7节电芯电压',
            value: '0'
          },
          {
            label: '第8节电芯电压',
            value: '0'
          },
        ]
      }
    ]
  })
  return (
    <View className="details-page">
      {
        state.dataSource.map(items => {
          return <Cell title={items.label} desc={items.value} />
        })
      }
      {
        state.column.map(item => {
          return (
            <CellGroup title={item.title} key={item.title}>
              {
                item.dataSource.map(items => {
                  return <Cell title={items.label} extra={items.value} />
                })
              }
            </CellGroup>
          )
        })
      }
    </View>
  )
}

export default Index
