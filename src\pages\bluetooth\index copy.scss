.bluetooth-page {
  padding: 16px;

  &-search {
    margin: 10px auto;

    &-block {
      width: 120px;
      height: 120px;
      margin: 0 auto;
      border-radius: 120px;
      animation: ripple_1 .6s linear infinite;
      background-color: #496AF2;
      overflow: hidden;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
    }
  }

  &-botton {
    height: 50px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;

    &-right {
      margin-left: 20px;
    }
  }

  &-body {

    &-title {
      padding: 10px 10px 0px 16px;
      display: flex;
    }

    &-block {
      width: 100%;

    }

    &-text {
      color: #496AF2;
      margin: 0px 2px;
    }

    &-list {
      padding: 10px 10px 10px 16px;

      &-li {
        display: flex;
        margin-bottom: 10px;
        align-items: center;
        border-bottom: 1px solid #f7f7f7;
        padding-bottom: 10px;

        &-text {
          margin-left: 5px;
        }
      }
    }


    .nut-cell {
      padding: 0px
    }

  }
}


/* 扫描 */
.scanning {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200px;
  height: 200px;
  background: linear-gradient(50deg, #ffffff 0%, transparent 50%);
  animation: investigation 2s infinite linear;
  transform-origin: left top;
}

@keyframes investigation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}