.map-fullscreen {
  width: 100%;
  height: 100vh;
  position: relative;
}

.location-btn {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  background: #07C160;
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
}

.BtnList{
  width: 70rpx;
  height: 175rpx;
  position: absolute;
  bottom: 514rpx;
  right: 0;
  z-index: 3;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 58rpx;
  margin-right: 24rpx;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: 20rpx;
}
.distance{
  width: 300px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 15px;
  font-weight: 600;
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  background-color: rgba(251, 251, 251, 0.739);
  padding-left: 10px;
}
.iconStyle{
  display: flex;
}
.flexCenter{
  width: 90%;
  display: flex;
  flex-direction: column;
  margin:3% 5%;
  .locationName{
    font-weight: 600;
    margin:1% 0%;
  }
  .locationAddress{
    font-weight: 100;
    font-size: 14px;
    margin:1% 0%;
  }
  Button{
    margin-top: 2%;
  }
}

.NavigationImage{
  width: 38rpx;
  height: 38rpx;
}

.nut-popup-bottom.round{
  z-index: 1000 !important;
}