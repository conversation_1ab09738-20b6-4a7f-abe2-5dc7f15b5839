import Request from '@/utils/request'

const getBladeSystemUserPhone = (data): any => {
    return Request({
        url: 'blade-system/user/getPhone',
        method: 'GET',
        data
    })
}

const getBladeSystemUserValidatePhone = (data): any => {
    return Request({
        url: 'blade-system/user/getValidatePhone',
        method: 'GET',
        data
    })
}

const getBladeSystemUserAppId = (data): any => {
    return Request({
        url: `blade-system/user/getAppId?appId=${data.appId}`,
        method: 'GET',
        data
    })
}

const getBladeSystemUserCkEquNoExist = (data): any => {
    return Request({
        url: `blade-system/user/ckEquNoExist?equNo=${data.equNo}`,
        method: 'GET',
        data
    })
}

const setBladeValidete = (data): any => {
    return Request({
        url: `blade-resource/sms/endpoint/send-validate?phone=${data.phone}`,
        method: 'POST',
        data: {}
    })
}

const postBladeSystemUserSave = (data): any => {
    return Request({
        url: `blade-system/user/save`,
        method: 'POST',
        data: data
    })
}

const postBladeUpQualityTime = (data): any => {
    return Request({
        url: `blade-system/user/upQualityTime`,
        method: 'POST',
        data: data
    })
}

const postBladeSystemUserDel = (data): any => {
    return Request({
        url: `blade-system/user/removeUser?phone=` + data.phone,
        method: 'POST',
        data: data
    })
}

const postBladeSystemUpUnBindEqu = (data): any => {
    return Request({
        url: `blade-system/user/upUnBindEqu`,
        method: 'POST',
        data: data
    })
}

const postBladeSystemUpBindEqu = (data): any => {
    return Request({
        url: `blade-system/user/upBindEqu`,
        method: 'POST',
        data: data
    })
}

const getBatteryData = (data) :any =>{
    return Request({
        url: `iotdb/IotEquipSource/getLastDevInfo`,
        method: 'GET',
        data: data
    })
}

const getDeviceVersion = (data) :any =>{
    return Request({
        url: `/iotdb/IotEquipSource/getDeviceVersion`,
        method: 'GET',
        data: data
    })
}

const getwxSendComC8 = (data) :any =>{
    return Request({
        url: `blade-base/batterControl/wxSendComC8`,
        method: 'GET',
        data: data
    })
}
const userBindDevice = (data) :any =>{
    return Request({
        url: `/blade-base/batterList/userBindDevice`,
        method: 'POST',
        data: data
    })
}
const getBatterAll = (data) :any =>{
    return Request({
        url: `/blade-base/batterList/getBatterAll`,
        method: 'GET',
        data: data
    })
}
const liftUserBindEqu = (data) :any =>{
    return Request({
        url: `/blade-base/batterList/liftUserBindEqu`,
        method: 'POST',
        data: data
    })
}
//反馈
const saveFeedbackLog = (data) :any =>{
    return Request({
        url: `/blade-base/feedback/saveFeedbackLog`,
        method: 'POST',
        data: data
    })
}
//图片
const putFile = (data) :any =>{
    return Request({
        url: `blade-base/customInfo/uploadFile`,
        method: 'POST',
        data: data,
    })
}

const saveUserInfo = (data) :any =>{
    return Request({
        url: `blade-system/user//update-info`,
        method: 'POST',
        data: data,
    })
}
export {
    getBladeSystemUserPhone,
    getBladeSystemUserValidatePhone,
    getBladeSystemUserCkEquNoExist,
    setBladeValidete,
    postBladeSystemUserSave,
    postBladeSystemUserDel,
    postBladeSystemUpUnBindEqu,
    postBladeSystemUpBindEqu,
    postBladeUpQualityTime,
    getBladeSystemUserAppId,
    getBatteryData,
    getDeviceVersion,
    getwxSendComC8,
    userBindDevice,
    getBatterAll,
    liftUserBindEqu,
    saveFeedbackLog,
    putFile,
    saveUserInfo
}