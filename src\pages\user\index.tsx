import React, { useEffect, useState ,useCallback} from 'react'
import { navigateTo } from '@tarojs/taro'
import { View,Image } from '@tarojs/components'
import {
  Cell,
  Tag,
  CollapseItem,
  Collapse,
  Dialog,
  Input,
  Button,
  Overlay
} from "@nutui/nutui-react-taro";
import userImg from '@/assets/images/LOGO1.png'
import './index.scss'
import Taro from '@tarojs/taro';
import {
  postBladeSystemUserDel,
  getBladeSystemUserValidatePhone,
  getBatterAll,
  liftUserBindEqu,
  getBladeSystemUserAppId,
  saveUserInfo
} from '../../api/index'
import './index.scss'
import { inject, observer } from 'mobx-react';
import refreshStore from '../../store/refreshStore';

const Index = observer(() => {
  const [state, setState] = useState({ phone:''})
  // let phone = Taro.getStorageSync('phone');
  const [isButtonVisible1, setIsButtonVisible1] = useState(false);
  const [showDialog, setShowDialog] = useState(false);
  const [isButtonVisible, setIsButtonVisible] = useState(false);
  const [equNo, setEquNo] = useState("");
  const [userId, setUserId] = useState('');
  const [form, setForm] = useState({seqNo: '',name: '',phone: '',carNo: '无',id:''})
  const [edit, setEdit] = useState(false);
  const [edit1, setEdit1] = useState(false);
  const [userName, setUserName] = useState('');
  const [carName, setCarName] = useState('');
  const [isOpen, setIsOpen] = useState(['0']);
  const [visible, setVisible] = useState(true)//提示
   const [autofocus, setAutofocus] = useState(true)//提示

    const isMatchDateis = useCallback(() => {
      let seqNo = Taro.getStorageSync('seqNo')
      if (seqNo) {
        setIsButtonVisible(true)
      } else {
        setIsButtonVisible(false)
      }
      let phone = Taro.getStorageSync('phone')
      getBladeSystemUserValidatePhone({ phone: phone }).then(res => {
        const data = res.data;
        console.log(data,'设备数据')
        setUserId(data.id)
        setForm({
          ...data
        })
       
        if (data.seqNo) {
          Taro.setStorageSync('seqNo', data.seqNo)
          setIsButtonVisible(true)
        }
        setEquNo("")
      })
    }, [])

    useEffect(() => {
      isMatchDateis()
    }, [isMatchDateis])

    //前往帮助与反馈
    const helpFeedback = () => {
      Taro.navigateTo({
          url: `/pages/map/index`
      })
    }
    //下载说明文档
    const setDownHpDoc = () => {
      Taro.showLoading({
        title: '打开说明文档中...'
      });
      var appId = Taro.getAccountInfoSync().miniProgram.appId;
      getBladeSystemUserAppId({ appId: appId }).then(res => {
        const data = res.data;
        Taro.downloadFile({
          url: data.hpDoc, //仅为示例，并非真实的资源
          success: (res) => {
  
            const fileSystemManager = Taro.getFileSystemManager();
  
            // 只要服务器有响应数据，就会把响应内容写入文件并进入 success 回调，业务需要自行判断是否下载到了想要的内容
            if (res.statusCode === 200) {
              fileSystemManager.saveFile({
                tempFilePath: res.tempFilePath,
                success: function (res) {
                  Taro.openDocument({
                    filePath: res.savedFilePath,
                    fileType: 'pptx',
                    success: function (res) {
                      Taro.showToast({
                        title: '文档打开成功',
                        icon: 'success',
                        duration: 2000
                      })
                      console.log("文档打开成功");
                      Taro.hideLoading();
                    },
                    fail: function (res) {
                      Taro.showToast({
                        title: '文档打开失败',
                        icon: 'none',
                        duration: 2000
                      })
                      Taro.hideLoading();
                    },
                  });
                },
                fail: function (error) {
                  console.log(error);
                  Taro.hideLoading();
                }
              })
            }
          },
          fail: (err) => {
            console.log(err);
            Taro.hideLoading();
          }
        })
      }).catch(_ => {
        Taro.hideLoading();
      })
    }

    const CellLinkArr = [
      {
        label: '电子保单',
        url: '/pages/userPage/myElectronic/index',
      },
    
    ]

    const CellEquArr = [
      {
        label: '我的设备',
        id:1,
      },
      {
        label: '我的设备',
        id:2,
      },
      {
        label: '我的设备',
        id:3,
      },
      {
        label: '我的设备',
        id:4,
      },
      {
        label: '我的设备',
        id:5,
      },
    ]

    const onJumpclick = (
      link: string,
    ) => {
      if (link) {
        navigateTo({ url: link })
      }
    }


    const onReset = () => {
      let phone = Taro.getStorageSync('phone')
      getBladeSystemUserValidatePhone({ phone: phone }).then((res) => {
        getBatterAll({userId:res.data.id}).then((resEle) => {
          resEle.data.forEach((ele) => {
            liftUserBindEqu({userId:res.data.id,deviceNo:ele.deviceNo}).then((res) => {
            })
          })
        })
      }).then(() => {
        postBladeSystemUserDel({ phone: phone }).then(res => {
          Taro.clearStorage();
          Taro.redirectTo({
            url: `/pages/register/index`,
          })
        })
      })
    }
    const handleSetName= (value,type) =>{
      console.log(value, type,'输入？');
      if(type == "用户名"){
        setUserName(value);
      }
      if(type == "车牌号"){
        setCarName(value)
      }
    }
    const saveUserName = () => {
      console.log( userName,carName,carName == '',userName == '');
      const isValidChars = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
      const basePattern = /^[\u4e00-\u9fa5][A-Z](?:[0-9A-Z]{5}|[DF][0-9A-Z]{5})$/;
      const isValidLength = userName.length >= 1 && userName.length <= 8;
      if(userName =='' && carName ==''){
        setEdit(false);
        return
      }
      if(!isValidChars.test(userName) && userName !=''){
        Taro.showToast({
          title: '不允许特殊符号！',
          icon: 'error',
          duration: 2000
        })
        clearInput()
        return
      }
      if(!isValidLength && userName != ''){
        Taro.showToast({
          title: '名字过长！',
          icon: 'error',
          duration: 2000
        })
        clearInput()
        return
      }
      if(!basePattern.test(carName) && carName !== ''){
        Taro.showToast({
          title: '车牌号格式错误！',
          icon: 'error',
          duration: 2000
        })
        clearInput()
        return
      }
      let userData = {
        realName:userName,
        id:userId,
        carNo:carName
      }
      console.log(userData);
      saveUserInfo(userData).then(res => {
        console.log(res,"成功了！");
        refreshStore.triggerRefresh();
        Taro.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000
        })
        isMatchDateis();
      })
      clearInput()
    }
    const clearInput = () => {
      setUserName('');
      setCarName('')
    }
    useEffect(() => {
      // seterrorText2("(国内)车牌号格式错误！\n正确示例：\n普通车牌：京A12345\n新能源车牌：京AD12345\n")
      // seterrorText3("提示:  需要中文或数字或英文,且组成4-30 个字符");
      let phone = Taro.getStorageSync('phone')
      if(phone == ""){
        setIsButtonVisible1(true);
      }
      setState({
        ...state,
        phone
      })
    }, [])
    useEffect(() => {
      setEdit (false);
      setEdit1 (false);
      clearInput();
      setIsOpen(['0'])
    }, [refreshStore.clearInputCount])
  return (
    <View className="user-page">
      <View className="user-page-content">
        <View className="user-page-avatar">
          <Image
          src={userImg} />
        </View>
        <View className='tag'>
          <Tag color="#4CBBB1" textColor="white">当前版本号: 1.4.23 </Tag>
        </View>
        <View className="user-page-title">ID:{state.phone}</View>
      </View>
       <Collapse activeName={isOpen} icon="right" iconSize="16" iconColor="#999" 
        onChange = {(value) => {
          if(value){
            setIsOpen(['1'])
          }else{
            setIsOpen(['0'])
          }
        }}  rotate = {90}>
        <CollapseItem title="用户信息" name="1">
          点击信息切换为编辑状态
         {!edit && <Cell key="name" desc={form['realName']} title="用户名" onClick={() => setEdit(true)}/>}
         {edit && <View className='editInput'>
            <View className='Overlay1' onClick={()=>{setEdit (false);clearInput()}}></View>
            <Input className='input1' defaultValue={form['realName']} name="text" label="用户名"  placeholder="请输入名字"  onChange={(value) => handleSetName(value,'用户名')}/>
            <View className='sureBtn' onClick={() => 
            {  
              setEdit(false)
              saveUserName();
            }
              }>确定</View>
            </View>
         }
          {!edit1 && <Cell key="carNo" desc={form['carNo']} title="车牌号" onClick={() => setEdit1(true)} />}
          {edit1 && <View className='editInput'>
              <View className='Overlay1'  onClick={()=>{setEdit1 (false);clearInput()}}></View>
              <Input  className='input1' defaultValue={form['carNo']} name="text" label="车牌号"  placeholder="请输入车牌号" onChange={(value) => handleSetName(value,'车牌号')}/>
              <View className='sureBtn' onClick={() => 
               { 
                setEdit1(false);
                saveUserName();
               }
                }>确定</View>
          </View>
         }
        </CollapseItem>
      </Collapse>
      {
        CellLinkArr.map(item => {
          return <Cell key={item.label} title={item.label} onClick={() => { onJumpclick(item.url) }} isLink />
        })
      }
       {/* <Cell
          center
          title={
            <View className='home-page-content-cell'>
              <View className='home-page-content-text'>售后服务</View>
            </View>
          }
          desc=""
        /> */}
        <Cell
          title={
            <View className='home-page-content-cell'>
              <View className='home-page-content-text'>使用说明</View>
            </View>
          }
          isLink onClick={() => { setDownHpDoc() }} />
        <Cell
          title={
            <View className='home-page-content-cell'>
              <View className='home-page-content-text'>帮助与反馈</View>
            </View>
          }
          isLink 
          onClick={() => helpFeedback()}
           /> 
      <View className="home-page-content-cell">
          {!isButtonVisible1 && <Button type='danger' onClick={() => { setShowDialog(true) }} style={{ marginBottom: 20 }} block>注 销</Button>}
          <Dialog
            title="注销须知"
            visible={showDialog}
            onOk={() => onReset()}
            onCancel={() => setShowDialog(false)}>
            注销会清除设备和用户信息，需要重新绑定新的设备信息，是否确认需要注销？
          </Dialog>
      </View>
    </View>
  )

}
)

export default inject(store => store)(Index);
