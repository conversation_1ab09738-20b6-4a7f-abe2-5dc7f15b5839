import _ from 'lodash'
import { twosComplementToDecimal } from '@/utils/todo'

let getNotifyArr = {
    1: '0003120000', //是否开关机
    2: '0003110000', //是否智能加热
    3: '0003040000',//是否启用备用电池
    4: '0003050000',//是否需要加热
    5: '0003070000',//是否需要复位
}

let getNotifyStatus = {
    '0': '待机',
    '1': '预充',
    '2': '放电',
    '3': '充电',
    '4': '待机',
    '5': '短路保护',
    '6': 'AFE异常',
    '7': '预充次数超限',
    '8': '关机',
    '9': 'NTC异常',
    '10': '电池断线',
    '11': '放电过流1',
    '12': '电池低电',
    '13': '充电过流',
    '14': '充电过压',
}

let getStatus = {
    '0': '在线',
    '1': '在线',
    '2': '在线',
    '3': '在线',
    '4': '在线',
    '5': '在线',
    '6': '在线',
    '7': '在线',
    '8': '离线',
    '9': '在线',
    '10': '在线',
    '11': '在线',
    '12': '在线',
    '13': '在线',
    '14': '在线',
}



let getNotifyRecord: any = {
    '电池编号': 4,
    '电流': 4,
    '剩余电量': 1,
    '累计循环放电次数': 2,
    '预计使用时间': 4,
    '软件版本': 2,
    '电池状态': 1,
    '硬件状态': 1,
    '温度': 14,
    '电池电压': 16,
    '告警': 1,
    '校验': 2,
}


const parseNotifyRecord = (data) => {
    let result = {};
    let startIndex = 0;

    for (let key in getNotifyRecord) {
        let length = getNotifyRecord[key];
        let value = data.slice(startIndex, startIndex + length);
        if (Array.isArray(value)) {
            value = value.reverse(); // 将数组反转，以符合大端字节序
        }
        result[key] = value;
        startIndex += length;
    }
    return result;
}

function hexToFloat(hex) {
    // 将十六进制字符串转换为32位有符号整数
    var intVal = parseInt(hex.replace(/\s/g, ""), 16);

    // 判断最高位是否为1，如果是则表示为负数
    if (intVal & 0x80000000) {
        intVal = intVal - 0xFFFFFFFF - 1;
    }

    // 将整数值除以10^2，保留两位小数
    var floatVal = (intVal / 100).toFixed(2);

    return parseFloat(floatVal);
}

const hexToBinary = (hex, numBits) => {
    let binary = parseInt(hex, 16).toString(2); // 将十六进制数转换为二进制字符串
    binary = binary.padStart(numBits, '0'); // 使用 padStart 方法补齐位数

    return binary;
}


const getParseNotifyRecord = (data: string) => {
    let NotifyData = _.cloneDeep(getNotifyRecord)
    let byte = [];
    for (let i = 0; i < data.length; i += 2) {
        byte.push(data.substr(i, 2));
    }
    let result = parseNotifyRecord(byte);
    console.log(result)
    for (let key in result) {
        let BATID: any = ''
        let byte = _.cloneDeep(result[key])
        if (key === '温度' || key === '电池电压') {
            BATID = []
            for (let i = 0; i < byte.length; i += 2) {
                BATID.push(`${byte[i]}${byte[i + 1]}`);
            }
        } else if (key === '校验') {
        } else {
            byte.map((_, index) => {
                //BATID += items
                if (key === '电池编号') {
                    BATID += byte[index].toString(10).padStart(2, byte[index])
                } else {
                    BATID += byte[index]
                }
            })
        }
        NotifyData[key] = BATID
        if (key === '电池编号') {
            NotifyData[key] = BATID
        } else {
            if (key === '温度' || key === '电池电压') {
                const relust = BATID.map(item => {
                    let num = twosComplementToDecimal(`0x${item}`)
                    if (key === '温度') {
                        return (num / 10).toFixed(1)
                    }
                    return (num / 1000).toFixed(3)
                })
                NotifyData[key] = relust
            } else if (key === '校验') {

            } else if (key === '硬件状态') {
                let binaryNumber = hexToBinary(BATID, 5);
                NotifyData[key] = binaryNumber
                console.log(binaryNumber);
            } else if (key === '告警') {
                let binaryNumber = _.cloneDeep(hexToBinary(BATID, 8));
                NotifyData[key] = binaryNumber.split("").reverse().join("")
            } else if (key === '电流') {
                console.log(BATID)
                let values = hexToFloat(BATID)
                console.log('电流', values)
                let binaryNumber = (values >= -0.3 && values <= 0.3) ? 0 : values
                NotifyData[key] = binaryNumber
            } else {
                if (key !== '电池编号') {
                    let values = twosComplementToDecimal(`0x${BATID}`)
                    if (key === '预计使用时间') {
                        NotifyData[key] = `${(values / 100).toFixed(2)}h`
                    } else if (key === '电流') {
                        NotifyData[key] = `${(values / 100).toFixed(2)}`
                    } else {
                        NotifyData[key] = values
                    }
                }
            }
        }
    }
    console.log(NotifyData);
    return NotifyData
}


export {
    getNotifyArr,
    getParseNotifyRecord,
    getNotifyStatus,
    getStatus
} 