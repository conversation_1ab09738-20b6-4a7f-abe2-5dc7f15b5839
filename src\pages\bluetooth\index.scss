.bluetooth-page {
  padding: 16px;

  &-SearchBar {
    width: 100%;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.06);
    border-radius: 30px;
    display: flex;
    align-items: center;
    font-size: 14px;
    background: #FFFFFF;
    padding: 0px 20px;

    .nut-input {
      padding-left: 21px;
      font-size: 14px;
    }
  }

  &-title {
    margin-top: 20px;
    font-size: 18px;
    font-weight: 500;
    color: #20355D;
    margin-bottom: 10px;
    display: flex;
    align-items: center;

    &-img {
      width: 20px;
      height: 16.66px;
      margin-left: 10px;
    }
  }

  &-search {
    margin: 0px auto;

    &-block {
      width: 200px;
      height: 200px;
      margin: 0 auto;
      border-radius: 120px;
      animation: ripple_1 .6s linear infinite;
      background-color: #DBEDFB;
      overflow: hidden;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;

      &-border {
        width: 170px;
        height: 170px;
        background-color: rgb(0 137 243 / 7%);
        border-radius: 120px;
        position: absolute;
      }

      &-img {
        width: 118.06px;
        height: 118.06px;
        position: absolute;
        z-index: 0;
      }
    }

    &-text {
      width: 100%;
      text-align: center;
      padding-top: 24px;
      font-size: 16px;
      font-weight: 500;
      color: #027DF9;
    }
  }

  &-botton {
    height: 50px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;

    &-right {
      margin-left: 20px;
    }
  }

  &-body {

    &-title {
      padding: 10px 10px 0px 16px;
      display: flex;
    }

    &-block {
      width: 100%;

    }

    &-text {
      color: #496AF2;
      margin: 0px 2px;
    }

    &-list {

      // padding: 10px 10px 10px 16px;


      &-li {
        width: 100%;
        height: 60px;
        background: #FFFFFF;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.06);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 12px;
        padding: 15px;

        &-row {
          display: flex;
          align-items: center;
        }

        &-img {
          width: 24px;
          height: 27.03px;
          margin-right: 20px;
        }

        &-text {
          font-size: 16px;
          font-weight: 500;
          color: #20355D;
        }

        &-button {
          width: 68px;
          height: 32px;
          line-height: 32px;
          background: #F2F7FF;
          border-radius: 18px;
          font-size: 14px;
          font-weight: 500;
          color: #027DF9;
          text-align: center;
        }
      }
    }


    .nut-cell {
      padding: 0px
    }

  }
}

.bluetooth-page-btn {
  margin-left: 187rpx;
  display: flex;
  width: 139rpx;
  gap: 20rpx;
}


/* 扫描 */
.scanning {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 280px;
  height: 280px;
  background: linear-gradient(50deg, #ffffff 0%, transparent 50%);
  animation: investigation 2s infinite linear;
  transform-origin: left top;
}



@keyframes investigation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.nut-button--normal{
  padding:0px;
}