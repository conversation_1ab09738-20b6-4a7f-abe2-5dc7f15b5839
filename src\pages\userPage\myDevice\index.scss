.myDevice-page {
  padding: 16px;
  padding-top: 5%;

  &-CellGroup {
    margin-bottom: 16px;
  }
  .onUnbindBtn{
    height:20px;
    width: 45px;
    background-color: #3861E3;
    border-radius: 3px;
    color:white;
    text-align: center;
    &:hover{
      background-color: #7c9bff;
    }
  }
  .switchBtn{
    height:20px;
    width: 45px;
    background-color: #23CA88;
    border-radius: 3px;
    color:white;
    text-align: center;
    &:hover{
      background-color: #85deba;
    }
  }
  .addEqu{
    display: flex;
    align-items: center;
    width: 280px;
  }
}

.container{
  width: 100%;
  border: 2rpx solid white;
  padding-bottom: 10%;
  border-radius: 10px;
  background-color: white;
  box-shadow: 2rpx 2rpx 4rpx 0rpx #ccc;
  .Title{
    font-weight: 600;
    text-align: center;
    margin: 7%;
  }
  .content{
    font-weight: 100;
    font-size:  30rpx;
    margin: 2%;
    margin-top: 6%;
  }
  .contentReason{
    font-size: 19px;
    text-indent: 0px;
    background-color: #4CBBB1;
    padding: 10px;
    color: white;
    font-weight: 600;
    border-radius: 0px 19px 0px 19px;
    margin-bottom: 2%;
  }
  .contentList{
    display: flex;
    flex-direction: column;
    gap: 15px;
    border: 5px dashed #4CBBB1;
    padding: 10px;
    border-radius: 11px;
    &-item{
      text-indent: 20px;
    }
  }
  .UploaderStyle{
    width: 100%;
    height: 20vh;
    background-color: white;
  }
}

.reBack{
  display: flex;
  align-items: center;
  height: 40px;
  width: 100%;
  gap: 3%;
  margin-bottom: 20px;
  padding-left: 12px;
  font-size: 15px;
  background-color: white;
  border-radius: 6px;
  box-shadow: 2rpx 2rpx 4rpx 0rpx #ccc;
  &:active{
    background-color: #cccccc97;
  }
}
.TextImageInfo{
  .TextAreaContent{
    padding: 16px;
  }
  .UploaderContent{
    padding-left: 16px;
    .UploaderStyle{
      
    }
  }
  .QuestionContent{
    background: #EFEFEF;
    border-radius: 20px;
  }
  .inputContent{
    margin: 19px 0px;
    padding: 10px 0px;
    background: #EFEFEF;
    border-radius: 12px;
  }
  .Title{
    // background-color: #4CBBB1 ; 
    padding: 20rpx;
    font-size: 25px;
  }
}
.buttonGroup{
  position:fixed;
  bottom: 0rpx;
  left: 0px;
  width: 100%;
  height: 100px;
  background-color: white;
  padding: 3%;
}


//组件样式修改
.nut-textarea{
  background:#F4F4F4;
  border-radius: 15px;
}
.nut-uploader__upload{
  border-radius: 20px;
  margin-bottom: 16px;
}
.nut-input{
  background: #EFEFEF;
}
.hint{
  font-size: 13px;
  color: black;
  margin-left: 16px;
}
.hint1{
  font-size: 13px;
  color: #939393;
  margin-left: 16px;
  margin-bottom: 5px;
}