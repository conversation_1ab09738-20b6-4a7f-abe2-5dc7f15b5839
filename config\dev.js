module.exports = {
  env: {
    NODE_ENV: '"development"',
  },
  defineConstants: {},
  mini: {
    /**
     * WebpackChain 插件配置
     * @docs https://github.com/neutrinojs/webpack-chain
     */
    webpackChain(chain) {
      // 解决 mini-css-extract-plugin CSS 顺序冲突警告
      chain.plugin("miniCssExtract").tap((args) => {
        args[0] = {
          ...args[0],
          ignoreOrder: true,
        };
        return args;
      });
    },
  },
  h5: {
    /**
     * WebpackChain 插件配置
     * @docs https://github.com/neutrinojs/webpack-chain
     */
    webpackChain(chain) {
      // 解决 mini-css-extract-plugin CSS 顺序冲突警告
      chain.plugin("miniCssExtract").tap((args) => {
        args[0] = {
          ...args[0],
          ignoreOrder: true,
        };
        return args;
      });
    },
  },
};
