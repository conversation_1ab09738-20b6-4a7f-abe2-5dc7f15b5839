import React from 'react'
import { View, Map } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { Cell,Button,Icon } from "@nutui/nutui-react-taro";
import './index.scss'
import { idea } from 'react-syntax-highlighter/dist/esm/styles/hljs';

function MapPage() {
  const  textGroundData = [
    {
      id:1,
      title:'一、为什么第一次接保护板电池总电压与保护板输出电压不一致？',
      desc:'描述文案'
    },
    {
      id:2,
      title:'二、使用过程中突然断电怎么办？',
      desc:'描述文案'
    },
    {
      id:3,
      title:'三、保护板为什么不能放电？',
      desc:'描述文案'
    },
    {
      id:4,
      title:'四、如何避免保护板误触发保护？',
      desc:'描述文案'
    },
    {
      id:5,
      title:'五、如何检查保护板的工作状态？',
      desc:'描述文案'
    },
    {
      id:6,
      title:'六、小程序为什么搜不到设备编号？',
      desc:'描述文案'
    },
  ]
  //跳转详情
  const skipone = (value) => {
    Taro.navigateTo(
      {
        url: `/pages/userPage/myDevice/index?value=${value}`
      }
    )
  }
  //跳转反馈
  const skipfeedback = () => {
    Taro.navigateTo(
      {
        url: `/pages/userPage/myDevice/index?feedback=${true}`
      }
    )
  }
  return (
    <View>

      <View className='cellGroup'>
         {
            textGroundData.map(items => {
              return (
                <Cell title={items.title} isLink  onClick={() => skipone(items.id)}/>
              )
            })
          }
      </View>
      
      <View className='buttonGroup'>
        <Button type="primary" size = "large" onClick={() => skipfeedback()} color ="#4CBBB1">问题反馈</Button>
      </View>
    </View>
  )
}

export default MapPage
