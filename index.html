<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <script>
        function lines(e) {
            if (!e) return new ArrayBuffer(0);
            var t = new ArrayBuffer(e.length),
                a = new DataView(t),
                n = 0,
                r = 0,
                o = e.length
            for (r; r < o; r += 2) {
                var i = parseInt(e.substr(r, 2), 16);
                a.setUint8(n, i), n++;
            }
            return t;
        }
        console.log(lines('1111'))
    </script>
</body>

</html>