import React, { useEffect, useState, useCallback } from 'react'
import Taro from '@tarojs/taro'
import {
  Icon
} from "@nutui/nutui-react-taro";
import { navigateTo } from '@tarojs/taro'
import { View, Image } from '@tarojs/components'

import { getBladeSystemUserValidatePhone, postBladeSystemUpBindEqu, postBladeUpQualityTime, getBladeSystemUserCkEquNoExist } from '../../../api/index'
import {
  Cell,
  CellGroup,
  Input,
  Button
} from "@nutui/nutui-react-taro";
import './index.scss'

function myDevicePage() {
  const [isButtonVisible, setIsButtonVisible] = useState(true);
  const [form, setForm] = useState({
    seqNo: '',
    name: '',
    phone: '',
    carNo: '无',
    qaEndTime: '',
    qaStartTime: ''
  })

  // 解绑设备
  const onQabind = () => {
    postBladeUpQualityTime(form).then(res => {
      setIsButtonVisible(false)
    })
  }

  const handleChange = (name, value) => {
    setForm({
      ...form,
      [name]: value
    })
  }

  // const onBind = () => {
  //   postBladeSystemUpBindEqu(form).then(res => {
  //     console.log(res.data)
  //     setForm({
  //       ...res.data
  //     })
  //     Taro.setStorageSync('seqNo', res.data.seqNo)
  //     setIsButtonVisible(true)
  //   })
  // }

  const isMatchDateis = useCallback(() => {
    let seqNo = Taro.getStorageSync('seqNo')
    if (seqNo) {
      setIsButtonVisible(true)
    } else {
      setIsButtonVisible(false)
    }
    let phone = Taro.getStorageSync('phone')
    getBladeSystemUserValidatePhone({ phone: phone }).then(res => {
      const data = res.data;
      
      setForm({
        ...data
      })
      if (data.seqNo) {
        Taro.setStorageSync('seqNo', data.seqNo)
        setIsButtonVisible(data.isQaTime == 0 ? true : false)
      }
    })
  }, [])

  const tapScan = async () => {
    Taro.scanCode({
      success: function (res) {
        const result = res.result
        const response = getBladeSystemUserCkEquNoExist({ equNo: res.result });
        response.then(status => {
          if (status.success) {
            handleChange('seqNo', result)
          }
        })
      }
    })
  }

  useEffect(() => {
    isMatchDateis()
  }, [isMatchDateis])

  return (
    <View className="myDevice-page">
      <CellGroup className='myDevice-page-CellGroup'>
        <Cell key="name" desc={form['seqNo']} title="电池编号" />
        <Cell key="name" desc={form['name']} title="用户名" />
        <Cell key="phone" desc={form['phone']} title="手机号" />
        <Cell
          center
          title='是否正品:'
          desc={<Image src="https://minio.coiot.net/jd-iot/正品险.png" style={{ width: 100, height: 30 }} />}
        />
        <Cell key="carModel" desc={form['carModel']} title="车型" />
        <Cell key="carNo" desc={form['carNo']} title="车牌" />
        {!isButtonVisible && <Cell key="qaStartTime"  descTextAlign="left" desc={<span style={{width: 200}}>{form['qaStartTime']}</span>} title="质保开始日期" />}
        {!isButtonVisible && <Cell key="qaEndTime"  descTextAlign="left" desc={<span style={{width: 200}}>{form['qaEndTime']}</span>} title="质保结束日期" />}
      </CellGroup>
      {isButtonVisible && <Button type='danger' onClick={onQabind} style={{ marginBottom: 20 }} block>该电池编号还未开通电子保单，点击开通</Button>}
      {/* {!isButtonVisible && <Button type='primary' onClick={onBind} style={{ marginBottom: 20 }} block>查看电子保单</Button>} */}
    </View>
  )
}

export default myDevicePage
