import React, { useEffect } from 'react'
import { useDidShow, useDidHide } from '@tarojs/taro'
import { Provider } from 'mobx-react'
import counterStore from './store/counter'
import BluetoothStore from './store/bluetooth'
import tarbarStore from './store/tarbar'
import bluetoothLink from './store/bluetoothLink'
const store = {
  counterStore,
  BluetoothStore,
  tarbarStore,
  bluetoothLink
}

// 全局样式
import './app.scss'
const App = (props) => {
  // 可以使用所有的 React Hooks
  useEffect(() => { })

  // 对应 onShow
  useDidShow(() => { })

  // 对应 onHide
  useDidHide(() => { })

  return (
    <Provider store={store}>
      {props.children}
    </Provider>
  )
}

export default App
