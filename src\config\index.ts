export const IS_DEV = process.env.NODE_ENV === 'development'
// 开发时本地可自己开启NeteaseCloudMusicApi服务(https://binaryify.github.io/NeteaseCloudMusicApi/#/)
const URL_ENV = {
    1: {
        weapp: 'https://wx.sloctopus.com/',
        // weapp: 'http://localhost:8009/',
        swan: 'https://server.xionghongyan.top',
        alipay: 'https://server.xionghongyan.top',
        tt: 'https://server.xionghongyan.top',
        h5: 'https://server.xionghongyan.top',
        rn: 'https://server.xionghongyan.top',
        qq: 'https://server.xionghongyan.top',
        jd: 'https://server.xionghongyan.top',
        quickapp: 'https://server.xionghongyan.top',
    },
    0: {
        weapp: 'https://wx.sloctopus.com/',
        // weapp: 'http://localhost:8009/',
        swan: 'https://server.xionghongyan.top',
        alipay: 'https://server.xionghongyan.top',
        tt: 'https://server.xionghongyan.top',
        h5: 'https://server.xionghongyan.top',
        rn: 'https://server.xionghongyan.top',
        qq: 'https://server.xionghongyan.top',
        jd: 'https://server.xionghongyan.top',
        quickapp: 'https://server.xionghongyan.top',
    }
}

export const BASE_URL = URL_ENV[+IS_DEV][process.env.TARO_ENV]
