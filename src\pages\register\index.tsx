import React, { useEffect, useState } from 'react'
import Taro from '@tarojs/taro'
import { View, Image, Text } from '@tarojs/components'
import { Button, Checkbox } from "@nutui/nutui-react-taro";
import { getBladeSystemUserPhone, getBladeSystemUserValidatePhone } from '../../api/index'
import './index.scss'
import Logo from '@/assets/images/LOGO1.png'

function registrationPage() {
  // const { isphone } = Taro.getCurrentInstance().router?.params || {};
  // if(!isphone){
  //   Taro.showToast({ title: '用户未登录', icon: "none", duration: 2000 })
  // }
  const onGetPhoneNumber = (res) => {
    const detail = res.detail
    if (detail.errMsg === 'getPhoneNumber:ok') {
      var appId = Taro.getAccountInfoSync().miniProgram.appId;
      getBladeSystemUserPhone({ code: detail.code, appId: appId }).then(res => {
        getBladeSystemUserValidatePhone({ phone: res.data }).then(values => {
          if (values.msg === 'FAIL') {
            Taro.navigateTo({
              url: `/pages/userPage/registration/index?phone=${res.data}`,
            })
          } else {
            Taro.setStorageSync('phone', res.data)
            if (values.data.seqNo) {
              Taro.setStorageSync('seqNo', values.data.seqNo)
            }
            Taro.switchTab({
              url: '/pages/home/<USER>',
            })
          }
        })
      })
    }
  }

  useEffect(() => {
    Taro.hideHomeButton()
  })

  const [agree, setAgree] = useState(false)

  const handleSkip = () => {
    Taro.setStorageSync('skipLogin', true)
    Taro.switchTab({
      url: '/pages/home/<USER>'
    }) 
  }

  return (
    <View className="register-page">
      <View className="logo-container">
        <Image src={Logo} className="logo" mode='widthFix' />
      </View>

      <View className="button-container">
        <View className='button-container-title'>
          <View>申请获取以下权限</View>
          <View className='button-container-title-item'>授权手机号登录</View>
        </View>
        <Button 
          className="auth-button"
          onGetPhoneNumber={onGetPhoneNumber} 
          openType='getPhoneNumber' 
          block 
          type="primary"
          color='#44BDB4'
          // disabled={!agree}
        >
          登录/注册
        </Button>
        <Button 
          className="skip-button"
          onClick={handleSkip}
          block 
          type="default"
        >
          游客登录
        </Button>
      </View>

      <View className="agreement">
        <Checkbox 
          checked={agree}
          onChange={(checked) => setAgree(checked)}
        />
        <View className="agreement-text">
          我已阅读并同意<Text className="link">《用户服务协议》</Text>和<Text className="link">《隐私政策》</Text>
        </View>
      </View>
    </View>
  )
}

export default registrationPage
