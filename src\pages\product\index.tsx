import React, { useState, useEffect } from 'react'
import { View, Map, Image } from '@tarojs/components'
import { Dialog,Button,Cell ,Icon,Popup} from '@nutui/nutui-react-taro'
import Taro from '@tarojs/taro'
import './index.scss'
import { getBladeSystemUserValidatePhone,getDeviceVersion } from '../../api/index'
import refreshStore from '../../store/refreshStore';
import { observer, inject } from 'mobx-react';
import Navigation from '@/assets/images/Navigation.png'

interface Polyline {
  points: { latitude: number; longitude: number }[]
  color: string
  width: number
  dottedLine: boolean, // 关键参数：虚线
  arrowLine: boolean  // 小箭头方向指示（可选）
}

const Index = observer(() => {
  let newLocation:any = {}
  let position:any = {}
  let phone = Taro.getStorageSync('phone');
  let seqNo = Taro.getStorageSync('seqNo');
  const [scale, setScale] = useState(18);
  const [userLocation, setUserLocation] = useState(true);
  const [distance,setDistance] = useState<any>(0)
  const [showBottom, setShowBottom] = useState(true);
  const [locationName, setLocationName] = useState('');
  const [locationAddress, setlocationAddress] = useState('');
  const [loading, setLoading] = useState(false);
  const [originalLocation, setOriginalLocation] = useState({
    longitude: 116.397428,
    latitude: 39.90923,
  });
  const [location, setLocation] = useState(originalLocation);
  const [markers, setMarkers] = useState([
    { 
      id: 0,
      longitude: location.longitude,
      latitude: location.latitude,
      iconPath: '',
      width: 0,
      height: 0
    }
  ]);
  const [polyline,setPolyline] = useState<Polyline[]>([
    {
      points: [],
      color: '#007AFF',
      width: 4,
      dottedLine: false, // 关键参数：虚线
      arrowLine: false  // 小箭头方向指示（可选）
    },
  ])


  // 改进的获取当前位置方法
  const getCurrentPosition = async () => {
    try {
      // 1. 检查权限状态
      const setting = await Taro.getSetting();
      if (!setting.authSetting['scope.userLocation']) {
        // 2. 如果没有权限，请求权限
        const res = await Taro.authorize({ scope: 'scope.userLocation' });
        if (!res.errMsg.includes('ok')) {
          throw new Error('用户拒绝了位置权限');
        }
      }

      // 3. 获取位置
      const locationRes = await Taro.getLocation({
        type: 'wgs84',
        isHighAccuracy: true,  // 开启高精度定位
      });

      console.log('定位成功:', locationRes);
      //自身定位
       position = {
        longitude: locationRes.longitude,
        latitude: locationRes.latitude
      };

      setScale(15)
      setLocation(position);
      return position;
    } catch (err) {

      setLocationName('--');
      setlocationAddress('--');

      console.error('定位失败:', err);
      let errMsg = '获取位置失败';
      
      if (err.errMsg.includes('auth deny')) {
        errMsg = '请打开位置权限设置';
        // 引导用户打开设置
        Taro.showModal({
          title: '提示',
          content: '需要位置权限才能使用此功能',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              Taro.openSetting();
            }
          }
        });
      } else if (err.errMsg.includes('fail')) {
        errMsg = '定位服务不可用，请稍后重试';
      }

      Taro.showToast({
        title: errMsg,
        icon: 'none',
        duration: 2000
      });
      throw err;
    }
  }

  const refreshData = () => {
    getCurrentPosition().catch(console.error);
  }

  const getlongitudeAndLatitude = () => {
    if(phone === ""){
      Taro.showToast({
        title: '用户未登录',
        icon: 'error',
        duration: 1000
      })
      return
    }
    if(seqNo == ""){
      getBladeSystemUserValidatePhone({phone:phone}).then((res) => {
        if(phone && res.data.seqNo == "" ){
          Taro.showToast({
            title: '用户未绑定设备',
            icon: 'error',
            duration: 1000
          })
          return
        }else if(phone && res.data.seqNo !== ""){
          getlonAndLat({devicesn:res.data.seqNo})
        }
      })
    }else{
      getlonAndLat({devicesn:seqNo})
    }
  }

  const refreshData1 = () => {
    getlongitudeAndLatitude();
  }

  //获取设备经纬
  const getlonAndLat = (devicesn) => {
    getDeviceVersion(devicesn).then((res) => {
      setLoading(false);
      Taro.hideLoading();
      setScale(4);
      setMarkers([]);
      if(res.data.lon === -1 && res.data.lat === -1){
        Taro.showToast({
          title: '设备定位失败!',
          icon: 'error',
          duration: 2000
        })
        setLocation({
          longitude: 116.397428,
          latitude: 39.90923,
        })
      }else{
        setScale(18);
        //设备位置
         newLocation = {
          longitude: res.data.lon,
          latitude: res.data.lat,
        };
        setOriginalLocation(newLocation);
        setLocation(newLocation);
        setMarkers([
          {
            id: 1,
            longitude: res.data.lon,
            latitude: res.data.lat,
            iconPath: require('../../assets/images/Marker.png'),
            width: 40,
            height: 40
          }
        ]);
      }
    })
  }

  //获取用户设备直线长度
  const getCurrentPosition1 = async () => {
    try {
      const setting = await Taro.getSetting();
      if (!setting.authSetting['scope.userLocation']) {
        const res = await Taro.authorize({ scope: 'scope.userLocation' });
        if (!res.errMsg.includes('ok')) {
          throw new Error('用户拒绝了位置权限');
        }
      }
      const locationRes = await Taro.getLocation({
        type: 'wgs84',
        isHighAccuracy: true,  
      });
       position = {
        longitude: locationRes.longitude,
        latitude: locationRes.latitude
      };
      getDeviceVersion({devicesn:seqNo}).then((res) => {
        setScale(4);
        if(res.data.lon === -1 && res.data.lat === -1){
          Taro.showToast({
            title: '设备定位失败!',
            icon: 'error',
            duration: 2000
          })
          return
        }else{
          setScale(18);
          //设备位置
          newLocation = {
            longitude: res.data.lon,
            latitude: res.data.lat,
          };
          setMarkers([
            {
              id: 1,
              longitude: res.data.lon,
              latitude: res.data.lat,
              iconPath: require('../../assets/images/Marker.png'),
              width: 40,
              height: 40
            }
          ]);
        }
      }).then(() => {
        setPolyline([{
            points: [
              position,
              newLocation
            ],
            color: '#007AFF',
            width: 4,
            dottedLine: true, // 关键参数：虚线
            arrowLine: true  // 小箭头方向指示（可选）
        },])
        setScale(15);
        setDistance(
          calculateDistance(
            newLocation.latitude,
            newLocation.longitude,
            position.latitude,
            position.longitude
          )
        )
      })
    } catch (err) {
      console.error('定位失败:', err);
      let errMsg = '获取位置失败';
      if (err.errMsg.includes('auth deny')) {
        errMsg = '请打开位置权限设置';
        // 引导用户打开设置
        Taro.showModal({
          title: '提示',
          content: '需要位置权限才能使用此功能',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              Taro.openSetting();
            }
          }
        });
      } else if (err.errMsg.includes('fail')) {
        errMsg = '定位服务不可用，请稍后重试';
      }
      Taro.showToast({
        title: errMsg,
        icon: 'none',
        duration: 2000
      });
      throw err;
    }
  }

  //两点经纬距离计算
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371e3 // 地球半径（米）
    const φ1 = lat1 * Math.PI / 180
    const φ2 = lat2 * Math.PI / 180
    const Δφ = (lat2 - lat1) * Math.PI / 180
    const Δλ = (lon2 - lon1) * Math.PI / 180

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return (R * c).toFixed(0)  // 返回示例："2345米"
  }

  //打开导航栏
  const openNavigation = () => {
    getNavigationInfo();
  }

  //获取导航信息
  const getNavigationInfo = () => {
    getDeviceVersion({devicesn:seqNo}).then((res) => {
      if(res.data.lon === -1 && res.data.lat === -1){
        console.log(res,'获取失败');
        setLocationName('--');
        setlocationAddress('--');
      }else{
        setLocationName(res.data.equNo);
        setlocationAddress(res.data.position == "暂时无上报定位数据" ? '--' :res.data.position);
        console.log(res,'???')
      }
    })
  }

  //跳转腾讯导航地图
  const navigateWithTencentMap = () => {
    getDeviceVersion({devicesn:seqNo}).then((res) => {
      if(res.data.lon === -1 && res.data.lat === -1){
        console.log(res,'获取失败');
        Taro.showToast({
          title: '获取设备信息失败!',
          icon: 'error',
          duration: 2000
        })
      }else{
        let lat = parseFloat(res.data.lat);
        let lon = parseFloat(res.data.lon);
        Taro.openLocation({
          latitude:lat,
          longitude:lon,
          name: "车辆位置",
          address: res.data.position,
          scale: 18,
          success: () => console.log('打开腾讯地图成功'),
          fail: (err) => {
            Taro.showToast({
              title: '打开地图失败!',
              icon: 'error',
              duration: 2000
            })
          }
        })
      }
    })
  }

  useEffect(() => {
    getCurrentPosition().catch(console.error);
     getCurrentPosition1();
     openNavigation();
  }, [refreshStore.deviceSwitchRefreshCount])

  useEffect(() => {
    getCurrentPosition().catch(console.error);
    getCurrentPosition1();
    openNavigation();
  }, [])

  return (
    <View className="map-fullscreen">
   <View className="map-fullscreen">
       <Popup visible={ showBottom } overlay= {false} style={{ height: '33%','zIndex':1 }} position="bottom" round  >
        <View className='flexCenter'>
          <View className='locationName'>{locationName}</View>
          <View className='locationAddress'>位置信息：{locationAddress} </View>
          <Button type="primary" onClick={() => navigateWithTencentMap()} disabled = {locationAddress == '--'}>前往地图导航</Button>
        </View>
       </Popup>
      <View className='distance'>
        <View>设备--自身距离：{distance/1000}公里</View>
      </View>
      <View className='BtnList'>
          <Button 
            type="primary"  
            size="small" 
            onClick={refreshData}  
            color='white'
            loading={loading}
            disabled={loading}
          >
          <View className='iconStyle'><Icon name="refresh2" color="black"></Icon></View>
          </Button> 
          <Button 
            type="primary"  
            size="small" 
            onClick={() => refreshData1()}
            color='white'
          >
           <View className='iconStyle'><Icon name="location" color="black"></Icon></View>
          </Button>
      </View>
      <Map
        id='map'
        longitude={location.longitude}
        latitude={location.latitude}
        scale={scale}
        maxScale={20}
        minScale={3}
        showLocation={userLocation}
        enableAutoMaxOverlooking={true}
        enable3D={true}
        enableBuilding={true}
        enableRotate={true}
        showCompass={true}
        markers={markers }
        polyline={polyline}
        style={{ width: '100%', height: '100vh' }}
      />
    </View>
    </View>
  );
})

export default inject(store => store)(Index)
