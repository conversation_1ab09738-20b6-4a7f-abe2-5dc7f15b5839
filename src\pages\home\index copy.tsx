import React, { useEffect, useState } from 'react'
import Taro from '@tarojs/taro'
import { View } from '@tarojs/components'
import {
  Button,
  Tag,
  CircleProgress,
  CellGroup,
  Cell,
  Switch,
  Animate,
  Row,
  Col,
  Icon,
  NoticeBar
} from "@nutui/nutui-react-taro";
import { inject, observer } from 'mobx-react';
import { getWriteBLEValue } from '@/utils/todo'
import { getNotifyArr, getParseNotifyRecord, getNotifyStatus } from '@/config/notify'
import './index.scss'

const ErrorStatus = [
  {
    title: '压差过大'
  },
  {
    title: '电压低'
  },
  {
    title: '电压高'
  },
  {
    title: 'MOS过温'
  },
  {
    title: '温度高充电功能关闭'
  },
  {
    title: '温度低充电功能关闭'
  },
  {
    title: '温度高放电功能关闭'
  },
  {
    title: '温度低放电功能关闭'
  }
]

const HomePage = observer(({ store }) => {

  const counterStore = store.counterStore.state
  const BluetoothStore = store.BluetoothStore.state
  const [state, setState] = useState<any>({
    checkedAsync: false,
    NotifyRecord: {
      '剩余电量': 0,
      '温度': [],
      '电池电压': []
    }
  })

  const handleBluetooth = () => {
    Taro.offBLECharacteristicValueChange(() => {
      // 跳转到目的页面，在当前页面打开
      Taro.redirectTo({
        url: '/pages/bluetooth/index',
      })
    })
  }

  useEffect(() => {
    if (counterStore.readId) {
      Taro.setBLEMTU({
        ...counterStore,
        mtu: 108,
        success: (res) => {
          console.log('success', res)
          notifyBLECharacteristicValueChange(counterStore);
        },
        fail: (res) => {
          console.log('fail', res)
        },
      })

    } else {
      // Taro.redirectTo({
      //   url: '/pages/bluetooth/index',
      // })
    }
    //        
  }, [store.counterStore.state])


  const notifyBLECharacteristicValueChange = (counterStore) => {
    console.log(counterStore.readId)
    console.log('可以读取的特征值')
    Taro.notifyBLECharacteristicValueChange({ // 启用监听设备特征值变化 
      state: true, // 启用 notify 功能
      ...counterStore,
      characteristicId: counterStore.readId,
      success(res) {
        console.log(res, '启用监听成功');
        onBLECharacteristicValueChange()
      },
      fail: (err) => {
        console.log(err, "启用监听失败");
      }
    });
  }


  const onBLECharacteristicValueChange = () => {
    // ArrayBuffer转16进制字符串示例
    function ab2hex(buffer) {
      let hexArr = Array.prototype.map.call(
        new Uint8Array(buffer),
        function (bit) {
          return ('00' + bit.toString(16)).slice(-2)
        }
      )
      return hexArr.join('');
    }
    Taro.onBLECharacteristicValueChange(res => {
      const date = ab2hex(res.value)
      console.log(res, '监听变化成功');
      console.log(date)
      console.log('上面是数据')
      const NotifyRecord = getParseNotifyRecord(date)
      store.BluetoothStore.setState(NotifyRecord)
    })
  }

  const writeBLECharacteristicValue = (buffer, fn) => {
    Taro.writeBLECharacteristicValue({
      ...counterStore,
      characteristicId: counterStore.writeDefaultId,
      value: buffer,
      writeType: 'write',
      success: (res) => {
        console.log('成功了了了')
        console.log(res)
        fn && fn()
      },
      fail: (err) => {
        console.log('失败了了了了')
        console.log(err);
        Taro.showToast({
          title: '操作失败!',
          duration: 2000
        })
      }
    })
  }

  const handleStart = (e) => {
    Taro.showLoading({
      title: '关机中...',
    })
    writeBLECharacteristicValue(getNotifyArr[1], () => {
      if (!e) {
        setTimeout(function () {
          store.counterStore.setState({
            deviceId: '',
            serviceId: '',
            characteristicId: '',
          })
          Taro.hideLoading()
        }, 3000)
      }
    })
  }

  const handleReset = (e, type) => {
    Taro.showLoading({
      title: '操作中...',
    })
    let buffer
    // 向蓝牙设备发送一个0x00的16进制数据
    const buffers = new ArrayBuffer(3)
    let dataView = new DataView(buffers)
    if (type === 1) {
      dataView.setUint8(0, 0xE3)
      dataView.setUint8(1, 0x01)
      dataView.setUint8(2, 0x1C)
      buffer = buffers
    } else if (type === 2) {
      dataView.setUint8(0, 0xE3)
      dataView.setUint8(1, 0x02)
      dataView.setUint8(2, 0x1C)
      buffer = buffers
    } else {
      dataView.setUint8(0, 0xE3)
      dataView.setUint8(1, 0x03)
      dataView.setUint8(2, 0x1C)
      buffer = buffers
    }
    writeBLECharacteristicValue(buffer, () => {
      setState({ ...state, checkedAsync: e })
      Taro.showToast({
        title: '操作成功!',
        icon: 'success',
        duration: 2000
      })
      // if (e) {

      // } else {
      //   Taro.showToast({
      //     title: '加热已关闭!',
      //     icon: 'success',
      //     duration: 2000
      //   })
      // }
    })
  }

  const onClickHeat = () => {
    Taro.showLoading({
      title: '操作中...',
    })
    writeBLECharacteristicValue(getNotifyArr[5], () => {
      Taro.showToast({
        title: '复位成功!',
        icon: 'success',
        duration: 2000
      })
    })
  }


  return (
    <View className="home-page">
      <View className="home-page-header">
        <View className='home-page-title'>
          <View>{BluetoothStore['电池编号']}</View>
          <View onClick={handleBluetooth}><Icon name="setting" size={20}></Icon></View>
        </View>
        <View>
          <Tag type="success">{getNotifyStatus[BluetoothStore['电池状态']]}</Tag>
          {/* <Tag style={{ marginLeft: 10 }} type="warning">{getNotifyStatus[BluetoothStore['电池状态']]}</Tag> */}
        </View>

        <View className="home-page-progress">
          <Animate type="float" loop={true}>
            <CircleProgress style={{ width: 140, height: 140 }} progress={+BluetoothStore['剩余电量']} strokeWidth={10} circleColor="#3b81ff" >
              电量{BluetoothStore['剩余电量']}%
            </CircleProgress>
          </Animate>
        </View>

        {/* <Cell title="启动" linkSlot={<Switch onChange={handleStart} checked />} /> */}
        <Button type='primary' style={{ marginBottom: 20 }} onClick={(e) => handleReset(e, 1)} block>智能加热开关</Button>
        <Button type='info' style={{ marginBottom: 20 }} onClick={(e) => handleReset(e, 2)} block>强启</Button>
        <Button type='danger' style={{ marginBottom: 20 }} onClick={(e) => handleReset(e, 2)} block>关机</Button>
        {/* <Button style={{ marginBottom: 20 }} onClick={(e) => handleReset(e, 2)} block>智能加热2</Button>
        <Button onClick={(e) => handleReset(e, 3)} block>智能加热2</Button> */}
        {/* <Cell title="智能加热1" linkSlot={<Switch checked={state.checkedAsync}  />
        <Cell title="智能加热2" linkSlot={<Switch checked={state.checkedAsync} onChange={(e) => handleReset(e, 2)} />} /> */}
        {/* <Cell title="智能加热3" linkSlot={<Switch checked={state.checkedAsync} onChange={(e) => handleReset(e, 3)} />} /> */}
        {/* <Cell title="智能加热2" linkSlot={<Switch checked={state.checkedAsync} onChange={(e) => handleReset(e, 1)} />} />
        <Cell title="智能加热3" linkSlot={<Switch checked={state.checkedAsync} onChange={(e) => handleReset(e, 1)} />} />
        <Cell title="智能加热4" linkSlot={<Switch checked={state.checkedAsync} onChange={(e) => handleReset(e, 1)} />} /> */}
        {/* <Cell title="复位" linkSlot={<Switch onChange={onClickHeat} />} /> */}
        {
          ErrorStatus.map((items, index) => {
            return +BluetoothStore['告警'][index] ? (
              <NoticeBar style={{ marginBottom: 5 }} scrollable={false}   >
                {items.title}
              </NoticeBar>
            ) : ''
          })
        }

        <Row className="home-page-grid" gutter="10">
          <Col span="12">
            <Cell icon="find" center title={BluetoothStore['电池电压'].length >= 1 ? `${BluetoothStore['电池电压'][0]}V` : ''} subTitle="电压" />
          </Col>
          <Col span="12">
            <Cell icon="my" center title={`${BluetoothStore['电流']}A`} subTitle="电流" />
          </Col>
          <Col span="12">
            <Cell icon="tips" center title={BluetoothStore['温度'].length >= 1 ? `${BluetoothStore['温度'][0]}℃` : ''} subTitle="电池温度" />
          </Col>
          <Col span="12">
            <Cell icon="refresh2" center title={BluetoothStore['累计循环放电次数']} subTitle="循环次数" />
          </Col>
        </Row>
        <Cell title="预计使用时间" icon="clock" desc={BluetoothStore['预计使用时间']} />
        {/* <Cell title="售后服务" icon="my" desc="4006876660" /> */}
        <Cell title="使用指导说明" icon="ask2" desc="点击查看" isLink />

      </View>

    </View>
  )
})

export default inject(store => store)(HomePage);

