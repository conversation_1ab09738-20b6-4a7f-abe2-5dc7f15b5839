import { observable } from 'mobx'

const BluetoothStore = observable({
    state: {
        '电池编号': '',
        '电流': 0,
        '剩余电量': '--',
        '累计循环放电次数': '--',
        '预计使用时间': '',
        '软件版本': '--',
        '电池状态': '8',
        '硬件状态': '--',
        '温度': [],
        '电池电压': [],
        '校验': '',
        '告警': ''
    },
    setState(props) {
        this.state = {
            ...this.state,
            ...props
        }
    }
})

export default BluetoothStore