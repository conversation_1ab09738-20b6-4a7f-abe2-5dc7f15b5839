import React from 'react'
import { View, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { inject, observer } from 'mobx-react';
import { getBladeSystemUserValidatePhone ,getBladeSystemUserCkEquNoExist} from '../api/index';
import SearchIcon from '../assets/images/icon_sousuo.png'
import {Popover,Button,Icon,Popup,Cell} from "@nutui/nutui-react-taro";
import { useState,useEffect } from 'react'
import './index.scss'
import refreshStore from './../store/refreshStore'
import bluetoothService from '@/services/bluetoothService'


const color = '#BFBFBF'
const selectedColor = '#027DF9'
const list = [
    {
        text: '首页',
        pagePath: '/pages/home/<USER>',
        iconPath: '../assets/images/tabs/home.png',
        selectedIconPath: '../assets/images/tabs/home_selected.png',
    },
    {
        text: '查找',
        pagePath: '/pages/product/index',
        iconPath: '../assets/images/tabs/product.png',
        selectedIconPath: '../assets/images/tabs/product_selected.png',
    },
    {
        text: '搜索',
        pagePath: '/pages/product/index',
        iconPath: '../assets/images/tabs/product.png',
        selectedIconPath: '../assets/images/tabs/product_selected.png',
    },
    {
        text: '详情',
        pagePath: '/pages/details/index',
        iconPath: '../assets/images/tabs/details.png',
        selectedIconPath: '../assets/images/tabs/details_selected.png',
    },
    {
        text: '我的',
        pagePath: '/pages/user/index',
        iconPath: '../assets/images/tabs/user.png',
        selectedIconPath: '../assets/images/tabs/user_selected.png',
    }
]

const DetailsPage = observer(({ store }) => { 
    let phone = Taro.getStorageSync('phone');
    const [showTop,setShowTop] = useState(false)
    const switchTab = async (index, url) => {
        console.log(index)

        store.tarbarStore.setState({
            selected: index
        })
        Taro.switchTab({ url })
        refreshStore.clearInput();
    }

    const handleBluetooth = async () => {

    }
    const itemList = [
        {name: '蓝牙',icon:"voice"},
        {name: '扫码',icon:"scan2"},
    ]
    const [lightTheme, setLightTheme] = useState(false)
 
    const clickMenu = (value) =>{
        if(value.name == "蓝牙"){
            if(!phone){
                Taro.redirectTo({
                    url: `/pages/register/index`,
                })
                return
            }
            Taro.setStorageSync('autoBluetooth',false);
            Taro.redirectTo({
                url: '/pages/bluetooth/index?IsCustom=true',
            })
        }else if(value.name == "扫码"){
            if(!phone){
                 Taro.redirectTo({
                    url: `/pages/register/index`,
                })
                return
            }
            Taro.switchTab({
                url: '/pages/home/<USER>',
            })
            Taro.setStorageSync('Ewm',true);
            refreshStore.clickEwm();
        }
    }

    const OpenPopover = () => {
        lightTheme ? setLightTheme(false) : setLightTheme(true);
    }

      useEffect(() => {
        if(Taro.getStorageSync('OpenPopover')){
            OpenPopover();
            Taro.removeStorageSync("OpenPopover");
        }
      }, [refreshStore.PopoverCount])

    return (
        <View className='tab-bar'>
            <View className='tab-bar-border'></View>
            {list.map((item, index) => {
                return item.text === '搜索' ? (
                    <View key={index} className='tab-bar-item' onClick={() => setShowTop(true)}>
                        <Popover 
                            offset={290}
                            visible={lightTheme} 
                            onClick={() => OpenPopover()} 
                            onChoose = {(event) => { clickMenu(event)}}
                            location = {'top'}
                            list={itemList}
                        >
                            <View className='tab-bar-search' >
                                <Image src={SearchIcon} />
                            </View>
                        </Popover>
                    </View>
                ) : (
                    <View key={index} className='tab-bar-item' onClick={() => switchTab(index, item.pagePath)}>
                        <Image src={store.tarbarStore.state.selected === index ? item.selectedIconPath : item.iconPath} />
                        <View style={{ color: store.tarbarStore.state.selected === index ? selectedColor : color }}>{item.text}</View>
                    </View>
                )
            })}
        </View>
    )
})


export default inject(store => store)(DetailsPage);
