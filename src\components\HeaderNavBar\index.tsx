import { Component } from 'react'
import { View, Text,Image } from '@tarojs/components';
import { NavBar } from '@nutui/nutui-react-taro';
import logo from '@/assets/images/LOGO2.png'
import './index.scss';

interface IProps {
  title?: string;
}

class HeaderNavBar extends Component<IProps> {
  static defaultProps = {
    title: '首页'
  }

  render() {
    const { title } = this.props;
    
    return (
      <View className='header-Logo'>
        <View className='header-Logo-content'>
          {/* <Text className='header-title'>{title}</Text> */}
           <Image src={logo} className='header-Logo-content-img' />
        </View>
      </View>
    );
  }
}

export default HeaderNavBar;
